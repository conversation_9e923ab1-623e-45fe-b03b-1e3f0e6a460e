/* ملف CSS للعامل المثالي */

/* التبويبات */
.tabs-container {
  margin-bottom: 2rem;
}

.tabs-header {
  display: flex;
  border-bottom: 2px solid #e0e0e0;
  margin-bottom: 1rem;
}

.tab-btn {
  background: none;
  border: none;
  padding: 1rem 2rem;
  cursor: pointer;
  font-size: 1rem;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tab-btn:hover {
  color: #2c3e50;
  background-color: #f8f9fa;
}

.tab-btn.active {
  color: #2c3e50;
  border-bottom-color: #3498db;
  font-weight: bold;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* نموذج العامل المثالي */
.ideal-employee-form {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.ideal-employee-form h2 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: #2c3e50;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #3498db;
}

.form-group input[readonly] {
  background-color: #f8f9fa;
  color: #6c757d;
}

.employee-search-input {
  position: relative;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.save-btn, .reset-btn {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.save-btn {
  background-color: #27ae60;
  color: white;
}

.save-btn:hover {
  background-color: #219a52;
  transform: translateY(-2px);
}

.reset-btn {
  background-color: #95a5a6;
  color: white;
}

.reset-btn:hover {
  background-color: #7f8c8d;
  transform: translateY(-2px);
}

/* جدول العمال المثاليين */
.ideal-employees-table-container {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.ideal-employees-table-container h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  text-align: center;
}

.table-controls {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
}

.reload-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  margin-left: 0.5rem;
}

.reload-btn:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
}

.export-btn {
  background-color: #27ae60;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.export-btn:hover {
  background-color: #219a52;
  transform: translateY(-2px);
}

.ideal-employees-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.ideal-employees-table th,
.ideal-employees-table td {
  padding: 0.75rem;
  text-align: center;
  border: 1px solid #ddd;
}

.ideal-employees-table th {
  background-color: #3498db;
  color: white;
  font-weight: bold;
}

.ideal-employees-table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.ideal-employees-table tr:hover {
  background-color: #e3f2fd;
}

/* تنسيق الصف المميز (آخر إضافة) */
.ideal-employees-table tr:first-child {
  background-color: #e8f5e8 !important;
  border: 2px solid #4CAF50;
}

.ideal-employees-table tr:first-child td {
  font-weight: bold;
}

/* أزرار الإجراءات */
.action-btn {
  padding: 0.25rem 0.5rem;
  margin: 0 0.25rem;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.edit-btn {
  background-color: #f39c12;
  color: white;
}

.edit-btn:hover {
  background-color: #e67e22;
}

.delete-btn {
  background-color: #e74c3c;
  color: white;
}

.delete-btn:hover {
  background-color: #c0392b;
}

/* قسم التقارير */
.reports-container {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.reports-container h2 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

/* فلاتر التقارير */
.report-filters {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.filter-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.filter-group {
  flex: 1;
  min-width: 200px;
}

.filter-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: #2c3e50;
}

.filter-group input,
.filter-group select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 0.9rem;
}

.filter-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.generate-btn, .print-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.generate-btn:hover, .print-btn:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
}

/* جدول التقارير */
.report-table-container {
  overflow-x: auto;
  margin-bottom: 2rem;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 1000px;
}

.report-table th,
.report-table td {
  padding: 0.75rem;
  text-align: center;
  border: 1px solid #ddd;
  font-size: 0.9rem;
}

.report-table th {
  background-color: #2c3e50;
  color: white;
  font-weight: bold;
  position: sticky;
  top: 0;
}

.report-table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.report-table tr:hover {
  background-color: #e3f2fd;
}

/* إحصائيات التقرير */
.report-statistics {
  display: flex;
  gap: 1rem;
  justify-content: space-around;
  flex-wrap: wrap;
  margin-top: 2rem;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 10px;
  text-align: center;
  min-width: 200px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.stat-info h4 {
  margin: 0.5rem 0;
  font-size: 1rem;
}

.stat-info span {
  font-size: 1.5rem;
  font-weight: bold;
}

/* Modal للتعديل - نفس تنسيق التدريب */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: white;
  border-radius: 10px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 10px 10px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.2rem;
}

.modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.modal-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  padding: 1rem 2rem;
  border-top: 1px solid #eee;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  background-color: #f8f9fa;
  border-radius: 0 0 10px 10px;
}

.save-btn, .cancel-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.save-btn {
  background-color: #27ae60;
  color: white;
}

.save-btn:hover {
  background-color: #219a52;
  transform: translateY(-2px);
}

.cancel-btn {
  background-color: #95a5a6;
  color: white;
}

.cancel-btn:hover {
  background-color: #7f8c8d;
  transform: translateY(-2px);
}

/* البحث في الجدول */
.table-search-input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 0.9rem;
  width: 250px;
  margin-left: 1rem;
}

.table-search-input:focus {
  outline: none;
  border-color: #3498db;
}

/* إحصائيات إضافية */
.extra-stat {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.extra-stat:nth-child(5) {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.extra-stat:nth-child(6) {
  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
}

/* تحسينات الجدول */
.ideal-employees-table tbody tr.hidden {
  display: none;
}

.ideal-employees-table tbody tr:hover {
  background-color: #e3f2fd;
  transform: scale(1.01);
  transition: all 0.2s ease;
}

/* تحسينات الأزرار */
.action-btn {
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* تحسينات النماذج */
.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  transform: scale(1.02);
  box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
}

/* تحسينات الإشعارات */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 1rem 2rem;
  border-radius: 5px;
  color: white;
  font-weight: bold;
  z-index: 10000;
  animation: slideIn 0.3s ease;
}

.notification.success {
  background-color: #27ae60;
}

.notification.error {
  background-color: #e74c3c;
}

.notification.info {
  background-color: #3498db;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* تحسينات للشاشات المتوسطة */
@media (max-width: 1024px) {
  .form-row {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .filter-row {
    flex-direction: column;
  }

  .filter-actions {
    flex-direction: column;
  }

  .report-statistics {
    flex-direction: column;
  }

  .modal-content {
    width: 95%;
    margin: 2% auto;
  }

  .table-search-input {
    width: 100%;
    margin: 0.5rem 0;
  }

  .table-controls {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* تنسيق فلاتر البحث المحددة */
.filtered-search-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.search-filters-row .form-group {
  display: flex;
  flex-direction: column;
}

.search-filters-row .form-group label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #495057;
  font-size: 14px;
}

.filter-input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.filter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  align-items: center;
}

.filter-actions .reset-btn,
.filter-actions .export-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.filter-actions .reset-btn {
  background-color: #6c757d;
  color: white;
}

.filter-actions .reset-btn:hover {
  background-color: #5a6268;
}

.filter-actions .export-btn {
  background-color: #28a745;
  color: white;
}

.filter-actions .export-btn:hover {
  background-color: #218838;
}