const express = require("express");
const { pool } = require("../config/database");
const { authenticateToken, checkPermission } = require("../middleware/auth");
const { logAction, createEditMessage } = require('../activityLogger');

const router = express.Router();

// متغير لتتبع ما إذا تم إنشاء الجدول أم لا
let contributionsTableCreated = false;

// إنشاء جدول المساهمات إذا لم يكن موجودًا
const createContributionsTable = async () => {
  if (contributionsTableCreated) return; // إذا تم إنشاء الجدول بالفعل، لا تفعل شيئاً
  try {
    await pool.promise().query(`
      CREATE TABLE IF NOT EXISTS contributions (
        id int NOT NULL AUTO_INCREMENT,
        employee_code varchar(50) NOT NULL,
        employee_name varchar(255) NOT NULL,
        contribution_type varchar(50) NOT NULL,
        company_amount decimal(10,2) NOT NULL,
        fund_amount decimal(10,2) NOT NULL,
        contribution_date date NOT NULL,
        notes text,
        created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        status varchar(50) DEFAULT 'active',
        PRIMARY KEY (id),
        KEY idx_employee_code (employee_code),
        KEY idx_contribution_type (contribution_type),
        KEY idx_contribution_date (contribution_date)
      ) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
    `);

    contributionsTableCreated = true; // تعيين المتغير إلى true بعد إنشاء الجدول
  } catch (error) {
    console.error('خطأ في إنشاء جدول المساهمات:', error);
    throw error;
  }
};

// إنشاء جدول المساهمات
router.get('/create-contributions-table', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    await createContributionsTable();
    res.json({ message: 'تم إنشاء جدول المساهمات بنجاح' });
  } catch (error) {
    console.error('خطأ في إنشاء جدول المساهمات:', error);
    res.status(500).json({ error: 'فشل في إنشاء جدول المساهمات' });
  }
});

// الحصول على جميع المساهمات
router.get('/', authenticateToken, checkPermission('view_contributions'), async (req, res) => {
  try {
    const [rows] = await pool.promise().query(`
      SELECT c.*, e.full_name as employee_name, e.department
      FROM contributions c
      LEFT JOIN employees e ON c.employee_code = e.code
      ORDER BY c.created_at DESC, c.id DESC
    `);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب المساهمات:', error);
    res.status(500).json({ error: 'فشل في جلب المساهمات' });
  }
});

// البحث في المساهمات
router.get('/search', authenticateToken, checkPermission('view_contributions'), async (req, res) => {
  try {
    const { 
      employee_code, 
      contribution_type, 
      start_date, 
      end_date, 
      min_amount, 
      max_amount,
      status 
    } = req.query;
    
    let query = `
      SELECT c.*, e.full_name as employee_name, e.department
      FROM contributions c
      LEFT JOIN employees e ON c.employee_code = e.code
      WHERE 1=1
    `;
    const params = [];
    
    if (employee_code) {
      query += " AND c.employee_code = ?";
      params.push(employee_code);
    }
    
    if (contribution_type) {
      query += " AND c.contribution_type = ?";
      params.push(contribution_type);
    }
    
    if (start_date) {
      query += " AND c.contribution_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND c.contribution_date <= ?";
      params.push(end_date);
    }
    
    if (min_amount) {
      query += " AND c.amount >= ?";
      params.push(min_amount);
    }
    
    if (max_amount) {
      query += " AND c.amount <= ?";
      params.push(max_amount);
    }
    
    if (status) {
      query += " AND c.status = ?";
      params.push(status);
    }
    
    query += " ORDER BY c.created_at DESC, c.id DESC";
    
    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث في المساهمات:', error);
    res.status(500).json({ error: 'فشل في البحث في المساهمات' });
  }
});

// إضافة مساهمة جديدة
router.post('/', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {


    await createContributionsTable();

    const {
      employee_code,
      employee_name,
      contribution_type,
      company_amount,
      fund_amount,
      contribution_date,
      notes,
      status = 'active'
    } = req.body;



    if (!employee_code || !contribution_type || !company_amount || !fund_amount || !contribution_date) {

      return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب أن تكون موجودة' });
    }

    // التحقق من عدم وجود مساهمة مكررة لنفس الموظف ونفس النوع في نفس التاريخ
    const [existingContributions] = await pool.promise().query(
      `SELECT id, employee_name, contribution_type, contribution_date
       FROM contributions
       WHERE employee_code = ? AND contribution_type = ? AND contribution_date = ?`,
      [employee_code, contribution_type, contribution_date]
    );

    if (existingContributions.length > 0) {
      const existing = existingContributions[0];
      return res.status(400).json({
        error: `يوجد مساهمة مماثلة للموظف ${employee_name || existing.employee_name} من نوع "${contribution_type}" في تاريخ ${existing.contribution_date}. لا يمكن إضافة مساهمات مكررة.`,
        existing_contribution: {
          id: existing.id,
          type: existing.contribution_type,
          contribution_date: existing.contribution_date
        }
      });
    }
    
    // الحصول على اسم الموظف إذا لم يتم إرساله
    let finalEmployeeName = employee_name;
    if (!finalEmployeeName) {
      const [employeeRows] = await pool.promise().query(
        "SELECT name FROM employees WHERE employee_code = ?",
        [employee_code]
      );
      finalEmployeeName = employeeRows.length > 0 ? employeeRows[0].name : null;
    }

    const [result] = await pool.promise().query(
      `INSERT INTO contributions (
        employee_code, employee_name, contribution_type, company_amount, fund_amount,
        contribution_date, notes, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        employee_code, finalEmployeeName, contribution_type, company_amount, fund_amount,
        contribution_date, notes, status
      ]
    );
    
    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'contributions',
      record_id: result.insertId.toString(),
      message: `تم إضافة مساهمة للموظف: ${finalEmployeeName} (كود: ${employee_code}) نوع: ${contribution_type} - مساهمة الشركة: ${company_amount} جنيه - مساهمة صندوق الزمالة: ${fund_amount} جنيه`
    });

    res.status(201).json({
      id: result.insertId,
      employee_code,
      employee_name: finalEmployeeName,
      contribution_type,
      company_amount,
      fund_amount,
      contribution_date,
      notes,
      status,
      message: 'تم إضافة المساهمة بنجاح'
    });
  } catch (error) {
    console.error('خطأ في إضافة المساهمة:', error);
    res.status(500).json({ error: 'فشل في إضافة المساهمة' });
  }
});

// تحديث مساهمة
router.put('/:id', authenticateToken, checkPermission('can_edit'), async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;



    // التحقق من وجود المساهمة والحصول على البيانات القديمة
    const [existingContribution] = await pool.promise().query(
      "SELECT * FROM contributions WHERE id = ?",
      [id]
    );

    if (existingContribution.length === 0) {
      return res.status(404).json({ error: 'المساهمة غير موجودة' });
    }

    const oldData = existingContribution[0];

    // التحقق من عدم وجود مساهمة مكررة (إذا تم تغيير البيانات الأساسية)
    if (updateData.employee_code || updateData.contribution_type || updateData.contribution_date) {
      const checkEmployeeCode = updateData.employee_code || oldData.employee_code;
      const checkContributionType = updateData.contribution_type || oldData.contribution_type;
      const checkContributionDate = updateData.contribution_date || oldData.contribution_date;

      const [duplicateContributions] = await pool.promise().query(
        `SELECT id, employee_name, contribution_type, contribution_date
         FROM contributions
         WHERE employee_code = ? AND contribution_type = ? AND contribution_date = ? AND id != ?`,
        [checkEmployeeCode, checkContributionType, checkContributionDate, id]
      );

      if (duplicateContributions.length > 0) {
        const existing = duplicateContributions[0];
        return res.status(400).json({
          error: `يوجد مساهمة مماثلة للموظف ${existing.employee_name} من نوع "${checkContributionType}" في تاريخ ${existing.contribution_date}. لا يمكن تحديث المساهمة لتصبح مكررة.`,
          existing_contribution: {
            id: existing.id,
            type: existing.contribution_type,
            contribution_date: existing.contribution_date
          }
        });
      }
    }

    // إزالة الحقول غير المسموح بتحديثها
    delete updateData.id;
    delete updateData.created_at;
    delete updateData.updated_at; // يتم تحديثه تلقائياً في قاعدة البيانات

    // إذا تم تغيير كود الموظف، تحديث اسم الموظف
    if (updateData.employee_code && updateData.employee_code !== existingContribution[0].employee_code) {
      const [employeeRows] = await pool.promise().query(
        "SELECT name FROM employees WHERE employee_code = ?",
        [updateData.employee_code]
      );

      updateData.employee_name = employeeRows.length > 0 ? employeeRows[0].name : null;
    }

    // قائمة حقول التاريخ التي تحتاج معالجة خاصة
    const dateFields = [
      'contribution_date', 'start_date', 'end_date'
    ];

    // معالجة حقول التاريخ - تحويل القيم الفارغة إلى null
    dateFields.forEach(field => {
      if (updateData.hasOwnProperty(field)) {
        if (updateData[field] === '' || updateData[field] === null || updateData[field] === undefined) {
          updateData[field] = null;
        }
      }
    });

    // تحضير البيانات للتحديث
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);

    if (fields.length === 0) {
      return res.status(400).json({ error: 'لم يتم توفير أي بيانات للتحديث' });
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    values.push(id);

    await pool.promise().query(
      `UPDATE contributions SET ${setClause} WHERE id = ?`,
      values
    );

    // إنشاء البيانات الجديدة للمقارنة
    const newData = { ...oldData, ...updateData };

    // إنشاء رسالة التعديل المفصلة
    const fieldLabels = {
      employee_code: 'كود الموظف',
      employee_name: 'اسم الموظف',
      contribution_type: 'نوع المساهمة',
      company_amount: 'مساهمة الشركة',
      fund_amount: 'مساهمة صندوق الزمالة',
      contribution_date: 'تاريخ المساهمة',
      notes: 'الملاحظات',
      status: 'الحالة'
    };

    const editMessage = createEditMessage(
      `مساهمة للموظف: ${newData.employee_name || newData.employee_code}`,
      oldData,
      newData,
      fieldLabels
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'contributions',
      record_id: id.toString(),
      message: editMessage
    });

    res.json({ message: 'تم تحديث المساهمة بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث المساهمة:', error);
    res.status(500).json({ error: 'فشل في تحديث المساهمة' });
  }
});

// حذف مساهمة
router.delete('/:id', authenticateToken, checkPermission('can_delete'), async (req, res) => {
  try {
    const { id } = req.params;

    // الحصول على معلومات المساهمة قبل الحذف
    const [contributionData] = await pool.promise().query(
      "SELECT employee_code, employee_name, contribution_type, company_amount, fund_amount FROM contributions WHERE id = ?",
      [id]
    );

    if (contributionData.length === 0) {
      return res.status(404).json({ error: 'المساهمة غير موجودة' });
    }

    const contribution = contributionData[0];

    // حذف المساهمة
    const [result] = await pool.promise().query(
      "DELETE FROM contributions WHERE id = ?",
      [id]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'contributions',
      record_id: id.toString(),
      message: `تم حذف مساهمة للموظف: ${contribution.employee_name} (كود: ${contribution.employee_code}) نوع: ${contribution.contribution_type} - مساهمة الشركة: ${contribution.company_amount} جنيه - مساهمة صندوق الزمالة: ${contribution.fund_amount} جنيه`
    });

    res.json({ message: 'تم حذف المساهمة بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف المساهمة:', error);
    res.status(500).json({ error: 'فشل في حذف المساهمة' });
  }
});

// الحصول على مساهمات موظف محدد
router.get('/employee/:employee_code', authenticateToken, checkPermission('view_contributions'), async (req, res) => {
  try {
    const { employee_code } = req.params;
    
    const [rows] = await pool.promise().query(`
      SELECT c.*, e.full_name as employee_name, e.department
      FROM contributions c
      LEFT JOIN employees e ON c.employee_code = e.code
      WHERE c.employee_code = ?
      ORDER BY c.created_at DESC, c.id DESC
    `, [employee_code]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب مساهمات الموظف:', error);
    res.status(500).json({ error: 'فشل في جلب مساهمات الموظف' });
  }
});

// الحصول على إحصائيات المساهمات
router.get('/statistics', authenticateToken, checkPermission('view_contributions'), async (req, res) => {
  try {
    const { start_date, end_date } = req.query;
    
    let dateFilter = "";
    const params = [];
    
    if (start_date && end_date) {
      dateFilter = " WHERE contribution_date BETWEEN ? AND ?";
      params.push(start_date, end_date);
    }
    
    // إجمالي المساهمات
    const [totalResult] = await pool.promise().query(
      `SELECT COUNT(*) as total_count, SUM(company_amount + fund_amount) as total_amount FROM contributions${dateFilter}`,
      params
    );

    // المساهمات حسب النوع
    const [typeResult] = await pool.promise().query(
      `SELECT contribution_type, COUNT(*) as count, SUM(company_amount + fund_amount) as total_amount
       FROM contributions${dateFilter}
       GROUP BY contribution_type
       ORDER BY total_amount DESC`,
      params
    );
    
    // أعلى المساهمين
    const [topContributors] = await pool.promise().query(
      `SELECT c.employee_code, c.employee_name, COUNT(*) as contribution_count, SUM(c.company_amount + c.fund_amount) as total_amount
       FROM contributions c${dateFilter}
       GROUP BY c.employee_code, c.employee_name
       ORDER BY total_amount DESC
       LIMIT 10`,
      params
    );
    
    res.json({
      total: totalResult[0],
      by_type: typeResult,
      top_contributors: topContributors
    });
  } catch (error) {
    console.error('خطأ في جلب إحصائيات المساهمات:', error);
    res.status(500).json({ error: 'فشل في جلب إحصائيات المساهمات' });
  }
});

module.exports = router;