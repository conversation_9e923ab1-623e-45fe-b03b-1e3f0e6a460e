// ملف JavaScript رئيسي موحد للمشروع

// استخدام المتغيرات من config.js
let API_URL = window.CONFIG ? window.CONFIG.API_URL : (localStorage.getItem('serverUrl') || "http://localhost:5500/api");
let serverRunning = window.GlobalState ? window.GlobalState.serverRunning : false;
let allEmployees = window.GlobalState ? window.GlobalState.allEmployees : [];
let filteredEmployees = window.GlobalState ? window.GlobalState.filteredEmployees : [];

// دوال مساعدة عامة
const Utils = {
  // تنظيف النص العربي
  normalizeArabicText(text) {
    if (!text) return '';
    return text
      .replace(/[أإآا]/g, 'ا')
      .replace(/ة/g, 'ه')
      .replace(/ى/g, 'ي')
      .replace(/ؤ/g, 'و')
      .replace(/ئ/g, 'ي')
      .replace(/ء/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  },

  // عرض القيم مع معالجة null
  displayValue(value) {
    return value === null || value === undefined ? '-' : value;
  },

  // معالجة أخطاء API - استخدام الدالة من shared-utils.js
  handleApiError(error, message) {
    if (window.SharedUtils && window.SharedUtils.handleApiError) {
      window.SharedUtils.handleApiError(error, message);
    } else {
      // fallback للكود القديم
      console.error('API Error:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.status,
        statusText: error.statusText
      });
      alert(message);
    }
  },

  // تنسيق التاريخ
  formatDate(dateString) {
    if (!dateString) return '-';
    if (typeof DateUtils !== 'undefined') {
      return DateUtils.formatDateFromDatabase(dateString);
    }
    return '';
  },

  // تنسيق الأرقام
  formatNumber(number) {
    if (!number) return '0';
    return new Intl.NumberFormat('ar-EG').format(number);
  },

  // إظهار مؤشر التحميل
  showLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
      element.innerHTML = `
        <div class="loading-indicator">
          <i class="fas fa-spinner fa-spin"></i>
          <p>جاري التحميل...</p>
        </div>
      `;
    }
  },

  // إخفاء مؤشر التحميل
  hideLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
      element.style.display = 'none';
    }
  },

  // تصدير البيانات إلى Excel
  exportToExcel(data, filename, sheetName = 'البيانات') {
    if (!window.XLSX) {
      alert('مكتبة Excel غير متوفرة');
      return;
    }

    const ws = XLSX.utils.json_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, sheetName);
    XLSX.writeFile(wb, `${filename}.xlsx`);
  },

  // تأكيد الحذف
  confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
  },

  // إظهار رسالة نجاح
  showSuccess(message) {
    // يمكن تطوير هذا لاحقاً لإظهار toast notifications
    alert(message);
  },

  // إظهار رسالة خطأ
  showError(message) {
    alert(message);
  }
};

// إدارة حالة الخادم
const ServerManager = {
  // التحقق من حالة تشغيل الخادم
  isRunning() {
    return serverRunning;
  },

  // التحقق من حالة الخادم
  async checkStatus() {
    try {
      const response = await fetch(`${API_URL}/status`);
      if (response.ok) {
        this.updateStatus(true);
        return true;
      }
    } catch (error) {
      console.error('Server check failed:', error);
      this.updateStatus(false);
      return false;
    }
    return false;
  },

  // تحديث حالة الخادم في الواجهة
  updateStatus(connected) {
    const statusBtn = document.getElementById('serverStatus');
    const toggleBtn = document.getElementById('toggleServer');
    
    if (!statusBtn || !toggleBtn) return;
    
    if (connected) {
      statusBtn.textContent = 'حالة السيرفر: متصل';
      statusBtn.className = 'status-btn connected';
      toggleBtn.textContent = 'إيقاف السيرفر';
      serverRunning = true;
      toggleBtn.disabled = false;
    } else {
      statusBtn.textContent = 'حالة السيرفر: غير متصل';
      statusBtn.className = 'status-btn disconnected';
      toggleBtn.textContent = 'تشغيل السيرفر';
      serverRunning = false;
      toggleBtn.disabled = false;
    }
  },

  // تبديل حالة الخادم
  async toggle() {
    const toggleBtn = document.getElementById('toggleServer');
    if (toggleBtn) {
      toggleBtn.disabled = true;
    }

    try {
      if (!serverRunning) {
        const response = await fetch(`${API_URL}/status`);
        if (response.ok) {
          this.updateStatus(true);
          if (window.loadEmployees) {
            await window.loadEmployees();
          }
        } else {
          throw new Error('فشل الاتصال بالسيرفر');
        }
      } else {
        this.updateStatus(false);
        const tableBody = document.getElementById('employeeTableBody');
        if (tableBody) {
          tableBody.innerHTML = '';
        }
        allEmployees = [];
      }
    } catch (error) {
      console.error('Server operation failed:', error);
      alert(serverRunning ? 'فشل في إيقاف السيرفر' : 'فشل في تشغيل السيرفر');
      this.updateStatus(serverRunning);
    } finally {
      if (toggleBtn) {
        toggleBtn.disabled = false;
      }
    }
  }
};

// إدارة البحث والفلترة
const SearchManager = {
  // البحث المتقدم
  advancedSearch(employee, searchText, department, job) {
    const normalizedSearch = Utils.normalizeArabicText(searchText.toLowerCase());
    const normalizedCode = Utils.normalizeArabicText(employee.code?.toString().toLowerCase() || '');
    const normalizedName = Utils.normalizeArabicText(employee.full_name?.toLowerCase() || '');
    const normalizedDepartment = Utils.normalizeArabicText(employee.department?.toLowerCase() || '');
    const normalizedJob = Utils.normalizeArabicText(employee.job_title?.toLowerCase() || '');

    const matchesSearch = !searchText || 
      normalizedCode.includes(normalizedSearch) ||
      normalizedName.includes(normalizedSearch);

    const matchesDepartment = !department || 
      normalizedDepartment === Utils.normalizeArabicText(department.toLowerCase());

    const matchesJob = !job || 
      normalizedJob === Utils.normalizeArabicText(job.toLowerCase());

    return matchesSearch && matchesDepartment && matchesJob;
  },

  // تحديث قوائم الفلترة
  updateFilters(employees) {
    const departmentFilter = document.getElementById('departmentFilter');
    const jobFilter = document.getElementById('jobFilter');

    if (!departmentFilter || !jobFilter) return;

    // مسح القوائم الحالية
    departmentFilter.innerHTML = '<option value="">كل الإدارات</option>';
    jobFilter.innerHTML = '<option value="">كل الوظائف</option>';

    // استخراج القيم الفريدة
    const departments = [...new Set(employees.map(emp => emp.department).filter(Boolean))].sort();
    const jobs = [...new Set(employees.map(emp => emp.job_title).filter(Boolean))].sort();

    // تحديث قائمة الإدارات
    departments.forEach(dept => {
      const option = document.createElement('option');
      option.value = dept;
      option.textContent = dept;
      departmentFilter.appendChild(option);
    });

    // تحديث قائمة الوظائف
    jobs.forEach(job => {
      const option = document.createElement('option');
      option.value = job;
      option.textContent = job;
      jobFilter.appendChild(option);
    });
  },

  // تطبيق البحث
  applySearch() {
    const searchInput = document.getElementById('searchInput');
    const departmentFilter = document.getElementById('departmentFilter');
    const jobFilter = document.getElementById('jobFilter');

    if (!searchInput || !departmentFilter || !jobFilter) return;

    const searchText = searchInput.value.trim();
    const department = departmentFilter.value;
    const job = jobFilter.value;

    console.log('Searching with:', { searchText, department, job });

    const filtered = allEmployees.filter(emp => 
      this.advancedSearch(emp, searchText, department, job)
    );

    console.log('Filtered results:', filtered.length);
    
    if (window.displayResults) {
      window.displayResults(filtered);
    }
  },

  // إعادة تعيين البحث
  resetSearch() {
    const searchInput = document.getElementById('searchInput');
    const departmentFilter = document.getElementById('departmentFilter');
    const jobFilter = document.getElementById('jobFilter');

    if (searchInput) searchInput.value = '';
    if (departmentFilter) departmentFilter.value = '';
    if (jobFilter) jobFilter.value = '';

    if (window.displayResults) {
      window.displayResults(allEmployees);
    }
  }
};

// إدارة النوافذ المنبثقة
const ModalManager = {
  // إظهار النافذة المنبثقة
  show(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.style.display = 'block';
      // تم حذف document.body.style.overflow لتجنب التعارض مع القائمة الجانبية
    }
  },

  // إخفاء النافذة المنبثقة
  hide(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.style.display = 'none';
      // تم حذف document.body.style.overflow لتجنب التعارض مع القائمة الجانبية
    }
  },

  // إعداد النافذة المنبثقة
  setup(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;

    // إغلاق عند النقر على X
    const closeBtn = modal.querySelector('.close, .close-modal');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hide(modalId));
    }

    // إغلاق عند النقر خارج النافذة
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.hide(modalId);
      }
    });

    // إغلاق عند الضغط على Escape
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && modal.style.display === 'block') {
        this.hide(modalId);
      }
    });
  }
};

// إدارة التبويبات
const TabManager = {
  // تفعيل تبويب
  activateTab(tabId) {
    // إخفاء جميع المحتويات
    document.querySelectorAll('.tab-content').forEach(content => {
      content.style.display = 'none';
    });

    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.classList.remove('active');
    });

    // إظهار المحتوى المطلوب
    const content = document.getElementById(tabId);
    if (content) {
      content.style.display = 'block';
    }

    // تفعيل الزر المطلوب
    const btn = document.querySelector(`[data-tab="${tabId}"]`);
    if (btn) {
      btn.classList.add('active');
    }
  },

  // إعداد التبويبات
  setup() {
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const tabId = btn.getAttribute('data-tab');
        if (tabId) {
          this.activateTab(tabId);
        }
      });
    });
  }
};

// إدارة النماذج
const FormManager = {
  // إعادة تعيين النموذج
  reset(formId) {
    const form = document.getElementById(formId);
    if (form) {
      form.reset();
    }
  },

  // جمع بيانات النموذج
  getData(formId) {
    const form = document.getElementById(formId);
    if (!form) return {};

    const formData = new FormData(form);
    const data = {};
    formData.forEach((value, key) => {
      data[key] = value;
    });
    return data;
  },

  // ملء النموذج بالبيانات
  fillData(formId, data) {
    const form = document.getElementById(formId);
    if (!form) return;

    Object.keys(data).forEach(key => {
      const field = form.querySelector(`[name="${key}"]`);
      if (field) {
        field.value = data[key] || '';
      }
    });
  },

  // التحقق من صحة النموذج
  validate(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;

    return form.checkValidity();
  }
};

// إعداد مستمعي الأحداث العامة
function setupEventListeners() {
  // إعداد التبويبات
  TabManager.setup();

  // إعداد البحث
  const searchBtn = document.getElementById('searchBtn');
  if (searchBtn) {
    searchBtn.addEventListener('click', () => SearchManager.applySearch());
  }

  const resetSearchBtn = document.getElementById('resetSearchBtn');
  if (resetSearchBtn) {
    resetSearchBtn.addEventListener('click', () => SearchManager.resetSearch());
  }

  const searchInput = document.getElementById('searchInput');
  if (searchInput) {
    searchInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        SearchManager.applySearch();
      }
    });
  }

  // إعداد فلاتر البحث
  const departmentFilter = document.getElementById('departmentFilter');
  if (departmentFilter) {
    departmentFilter.addEventListener('change', () => SearchManager.applySearch());
  }

  const jobFilter = document.getElementById('jobFilter');
  if (jobFilter) {
    jobFilter.addEventListener('change', () => SearchManager.applySearch());
  }

  // إعداد التحكم في الخادم
  const toggleServerBtn = document.getElementById('toggleServer');
  if (toggleServerBtn) {
    toggleServerBtn.addEventListener('click', () => ServerManager.toggle());
  }
}

// دالة التهيئة (يتم استدعاؤها من الصفحات التي تحتاجها)
function initializeMainJS() {
  // إعداد مستمعي الأحداث
  setupEventListeners();

  // التحقق من حالة الخادم
  ServerManager.checkStatus();

  // إعداد النوافذ المنبثقة الموجودة
  document.querySelectorAll('.modal').forEach(modal => {
    ModalManager.setup(modal.id);
  });
}

// تصدير الدوال للاستخدام العام
window.Utils = Utils;
window.ServerManager = ServerManager;
window.SearchManager = SearchManager;
window.ModalManager = ModalManager;
window.TabManager = TabManager;
window.FormManager = FormManager;


