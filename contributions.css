/* تنسيقات خاصة بصفحة المساهمات */
@import url('shared-styles.css');

/* تنسيق الصفحة الرئيسية */
.contributions-page {
  background: #f8f9fa;
  direction: rtl;
  text-align: right;
}

.contributions-page * {
  text-align: right;
}

.contributions-page .form-group label {
  text-align: right;
}

.contributions-page .tabs {
  direction: rtl;
  justify-content: flex-start;
}

.contributions-page .tab-btn {
  margin-right: 5px;
  margin-left: 0;
}



.contributions-page h1,
.contributions-page h2,
.contributions-page h3 {
  text-align: right;
}

/* تم نقل تنسيقات التبويبات إلى ملف shared-styles.css */

/* تنسيق نموذج المساهمة - تم نقل التنسيقات المشتركة إلى ملف shared-styles.css */
.contribution-form {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  color: #333;
}

/* تم نقل تنسيقات حقول النماذج إلى ملف shared-styles.css */

.form-actions {
  grid-column: span 4;
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

/* تم نقل تنسيقات الأزرار إلى ملف shared-styles.css */

/* تم نقل تنسيقات تأثيرات التحويم للأزرار إلى ملف shared-styles.css */

/* تم نقل تنسيقات الجداول إلى ملف shared-styles.css */

/* تم نقل تنسيقات الأزرار والتحكم بالجداول إلى shared-styles.css */

/* تنسيق جدول المساهمات */
.contribution-table-container {
  margin-top: 30px;
}

.contribution-table-container h3 {
  margin-bottom: 15px;
}

/* تنسيق الصف المميز (آخر إضافة) */
.contribution-table tr:first-child {
  background-color: #e8f5e8 !important;
  border: 2px solid #4CAF50;
}

.contribution-table tr:first-child td {
  font-weight: bold;
}

.loading-indicator {
  display: inline-block;
  margin-right: 15px;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
  color: #333;
  font-weight: bold;
}

/* تم نقل تنسيقات الجداول إلى shared-styles.css */

/* تنسيق أزرار الإجراءات في الجدول */
.table-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}

/* تلوين خانات المساهمات */
/* خانة مساهمة الشركة - اللون الأخضر (العمود الرابع) */
.contribution-table td:nth-child(4) {
  background-color: rgba(40, 167, 69, 0.1) !important;
  color: #155724 !important;
  font-weight: 600;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.contribution-table th:nth-child(4) {
  background-color: rgba(40, 167, 69, 0.2) !important;
  color: white !important;
}

/* خانة مساهمة صندوق الزمالة - اللون الأزرق (العمود الخامس) */
.contribution-table td:nth-child(5) {
  background-color: rgba(25, 118, 210, 0.1) !important;
  color: #0d47a1 !important;
  font-weight: 600;
  border: 1px solid rgba(25, 118, 210, 0.3);
}

.contribution-table th:nth-child(5) {
  background-color: rgba(25, 118, 210, 0.2) !important;
  color: white !important;
}

/* تلوين الخانات باستخدام الكلاسات أيضاً */
.contribution-table .company-amount {
  background-color: rgba(40, 167, 69, 0.1) !important;
  color: #155724 !important;
  font-weight: 600;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.contribution-table .fund-amount {
  background-color: rgba(25, 118, 210, 0.1) !important;
  color: #0d47a1 !important;
  font-weight: 600;
  border: 1px solid rgba(25, 118, 210, 0.3);
}

/* تلوين جدول التقارير التفصيلية */
#detailsTable .company-amount {
  background-color: rgba(40, 167, 69, 0.1) !important;
  color: #155724 !important;
  font-weight: 600;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

#detailsTable .fund-amount {
  background-color: rgba(25, 118, 210, 0.1) !important;
  color: #0d47a1 !important;
  font-weight: 600;
  border: 1px solid rgba(25, 118, 210, 0.3);
}

/* تلوين جدول المساهمات المفلترة */
#filteredContributionsTable td:nth-child(4) {
  background-color: rgba(40, 167, 69, 0.1) !important;
  color: #155724 !important;
  font-weight: 600;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

#filteredContributionsTable td:nth-child(5) {
  background-color: rgba(25, 118, 210, 0.1) !important;
  color: #0d47a1 !important;
  font-weight: 600;
  border: 1px solid rgba(25, 118, 210, 0.3);
}

#filteredContributionsTable th:nth-child(4) {
  background-color: rgba(40, 167, 69, 0.2) !important;
  color: white !important;
}

#filteredContributionsTable th:nth-child(5) {
  background-color: rgba(25, 118, 210, 0.2) !important;
  color: white !important;
}

.edit-contribution-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 5px;

  display: inline-block;
  position: relative;
  z-index: 10;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.edit-contribution-btn i {
  pointer-events: none; /* منع الأيقونة من التداخل مع النقر */
}

.edit-contribution-btn:hover {
  background-color: #0056b3;
}

.delete-contribution-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;

  display: inline-block;
  position: relative;
  z-index: 10;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.delete-contribution-btn i {
  pointer-events: none; /* منع الأيقونة من التداخل مع النقر */
}

.delete-contribution-btn:hover {
  background-color: #c82333;
}

/* تنسيق فلاتر البحث */
.search-filters {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.filter-actions {
  grid-column: span 3;
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

/* تم نقل تنسيقات أزرار البحث إلى shared-styles.css */

/* تنسيق النافذة المنبثقة */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
  background-color: #fefefe;
  margin: 5% auto;
  padding: 0;
  border: 1px solid #888;
  width: 70%;
  max-width: 700px;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modal-header {
  padding: 15px 20px;
  background-color: #2196F3;
  color: white;
  border-radius: 5px 5px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.close {
  color: white;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: #f1f1f1;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  border-radius: 0 0 5px 5px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancel-btn {
  background-color: #ccc;
  color: #333;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;

}

.cancel-btn:hover {
  background-color: #bbb;
}

/* تنسيق قسم التقارير */
.report-period-title {
  text-align: center;
  font-size: 2rem;
  color: #2c3e50;
  margin: 20px 0 30px;
  padding: 15px 30px;
  background-color: #ffffff;
  border-radius: 50px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.report-period-title::before {
  content: '📊';
  margin-left: 15px;
  font-size: 1.8rem;
  vertical-align: middle;
}

.report-period-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, #ffffff, #2ecc71, #e74c3c, #f39c12);
  background-size: 400% 100%;
}

.report-section {
  margin-bottom: 40px;
  background-color: #ffffff;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.report-section h3 {
  margin-top: 0;
  margin-bottom: 30px;
  color: #2c3e50;
  font-size: 1.6rem;
  position: relative;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
  text-align: center;
}

.report-section h3::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  left: 50%;
  margin-left: -50px;
  width: 100px;
  height: 3px;
  background: linear-gradient(to right, #ffffff, #2ecc71);
  border-radius: 3px;
}

.summary-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
  padding: 10px;
  position: relative;
}

@media (max-width: 1200px) {
  .contribution-form {
    grid-template-columns: 1fr 1fr 1fr;
  }
  .form-actions {
    grid-column: span 3;
  }
}

@media (max-width: 900px) {
  .contribution-form {
    grid-template-columns: 1fr 1fr;
  }
  .form-actions {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  .contribution-form {
    grid-template-columns: 1fr;
  }
  .form-actions {
    grid-column: span 1;
  }

  .summary-cards-container {
    grid-template-columns: 1fr;
  }
}

/* تم إزالة التأثيرات المتحركة للبطاقات لتحسين الأداء */

.summary-card {
  opacity: 1;
}

.company-card:nth-child(1) { --card-index: 1; }
.company-card:nth-child(2) { --card-index: 2; }
.fund-card:nth-child(3) { --card-index: 3; }
.fund-card:nth-child(4) { --card-index: 4; }
.total-card:nth-child(5) { --card-index: 5; }

.report-section h4 {
  color: #ffffff;
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  text-align: center;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
}

.report-section h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, #ffffff, transparent);
}

/* تم إزالة التأثيرات المتحركة للجدول لتحسين الأداء */

.contribution-table tbody tr {
  opacity: 1;
}

.card-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
  background: rgba(240, 240, 240, 0.8);
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #333333;
  border: 1px solid #ddd;
}

.summary-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 20px;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);

  position: relative;
  overflow: hidden;
  border: 2px solid #e0e0e0;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 5px;
  height: 100%;
  border-radius: 0 16px 16px 0;
}

/* تنسيق بطاقات الشركة */
.company-card::before {
  background: linear-gradient(to bottom, #ffffff, #f8f9fa);
}

.company-card .card-icon {
  color: #333333;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #ddd;
}

.company-card .summary-value {
  color: #333333;
}

/* تنسيق بطاقات صندوق الزمالة */
.fund-card::before {
  background: linear-gradient(to bottom, #27ae60, #2ecc71);
}

.fund-card .card-icon {
  color: #27ae60;
  background: rgba(46, 204, 113, 0.1);
}

.fund-card .summary-value {
  color: #27ae60;
}

/* تنسيق بطاقة الإجمالي */
.total-card::before {
  background: linear-gradient(to bottom, #9b59b6, #8e44ad);
}

.total-card .card-icon {
  color: #9b59b6;
  background: rgba(155, 89, 182, 0.1);
}

.total-card .summary-value {
  color: #9b59b6;
}

/* تنسيق بطاقة الموظفين */
.employees-card::before {
  background: linear-gradient(to bottom, #9b59b6, #8e44ad);
}

.employees-card .card-icon {
  color: #9b59b6;
  background: rgba(155, 89, 182, 0.1);
}

.employees-card .summary-value {
  color: #9b59b6;
}

/* تنسيقات إضافية لبطاقات المساهمات البيضاء - تجاوز التنسيقات العامة */
#contribution-reports .summary-card {
  background: #ffffff !important;
  color: #333333 !important;
  border: 2px solid #e0e0e0 !important;
}

#contribution-reports .summary-card .summary-title {
  color: #333333 !important;
}

#contribution-reports .summary-card .summary-value {
  color: #333333 !important;
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: #333333 !important;
  background-clip: unset !important;
}

#contribution-reports .summary-card .summary-unit {
  color: #666666 !important;
}

#contribution-reports .company-card::before {
  background: linear-gradient(to bottom, #ffffff, #f8f9fa) !important;
}

#contribution-reports .company-card .card-icon {
  color: #333333 !important;
  background: rgba(240, 240, 240, 0.8) !important;
  border: 1px solid #ddd !important;
}

#contribution-reports .company-card .summary-value {
  color: #333333 !important;
}

#contribution-reports .total-card::before {
  background: linear-gradient(to bottom, #ffffff, #f8f9fa) !important;
}

#contribution-reports .total-card .card-icon {
  color: #333333 !important;
  background: rgba(240, 240, 240, 0.8) !important;
  border: 1px solid #ddd !important;
}

#contribution-reports .total-card .summary-value {
  color: #333333 !important;
}

/* تنسيقات بطاقات صندوق الزمالة - تحويلها للأبيض أيضاً */
#contribution-reports .fund-card::before {
  background: linear-gradient(to bottom, #ffffff, #f8f9fa) !important;
}

#contribution-reports .fund-card .card-icon {
  color: #333333 !important;
  background: rgba(240, 240, 240, 0.8) !important;
  border: 1px solid #ddd !important;
}

#contribution-reports .fund-card .summary-value {
  color: #333333 !important;
}

/* تنسيقات بطاقة الموظفين - تحويلها للأبيض أيضاً */
#contribution-reports .employees-card::before {
  background: linear-gradient(to bottom, #ffffff, #f8f9fa) !important;
}

#contribution-reports .employees-card .card-icon {
  color: #333333 !important;
  background: rgba(240, 240, 240, 0.8) !important;
  border: 1px solid #ddd !important;
}

#contribution-reports .employees-card .summary-value {
  color: #333333 !important;
}

/* تنسيق عام لجميع الأيقونات في بطاقات المساهمات */
#contribution-reports .card-icon {
  color: #333333 !important;
  background: rgba(240, 240, 240, 0.8) !important;
  border: 1px solid #ddd !important;
}

/* تنسيق أزرار عرض التفاصيل في بطاقات المساهمات */
#contribution-reports .details-btn {
  background-color: #ffffff !important;
  color: #333333 !important;
  border: 2px solid #007bff !important;
}

#contribution-reports .details-btn:hover {
  background-color: #007bff !important;
  color: #ffffff !important;
}

/* تأكيد إضافي لتطبيق الخلفية البيضاء على جميع البطاقات */
.tab-content#contribution-reports .summary-card,
.tab-content#contribution-reports .company-card,
.tab-content#contribution-reports .fund-card,
.tab-content#contribution-reports .total-card,
.tab-content#contribution-reports .employees-card {
  background: #ffffff !important;
  background-color: #ffffff !important;
  background-image: none !important;
}

/* إزالة أي تدرجات لونية زرقاء */
.tab-content#contribution-reports .summary-card::before,
.tab-content#contribution-reports .company-card::before,
.tab-content#contribution-reports .fund-card::before,
.tab-content#contribution-reports .total-card::before,
.tab-content#contribution-reports .employees-card::before {
  background: #ffffff !important;
  background-image: none !important;
}

/* تنسيقات قوية جداً لضمان تطبيق اللون الأبيض */
body.contributions-page #contribution-reports .summary-card,
body.contributions-page #contribution-reports .company-card,
body.contributions-page #contribution-reports .fund-card,
body.contributions-page #contribution-reports .total-card,
body.contributions-page #contribution-reports .employees-card {
  background: #ffffff !important;
  background-color: #ffffff !important;
  background-image: none !important;
  color: #333333 !important;
}

body.contributions-page #contribution-reports .summary-card .summary-value,
body.contributions-page #contribution-reports .company-card .summary-value,
body.contributions-page #contribution-reports .fund-card .summary-value,
body.contributions-page #contribution-reports .total-card .summary-value,
body.contributions-page #contribution-reports .employees-card .summary-value {
  color: #333333 !important;
  background: none !important;
  background-image: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: #333333 !important;
  background-clip: unset !important;
}

/* تم إزالة تأثيرات hover للبطاقات لتحسين الأداء */

.summary-title {
  width: 100%;
  text-align: center;
  font-weight: bold;
  color: #2c3e50;
  font-size: 1.1rem;
  margin-bottom: 20px;
  position: relative;
  padding: 0 10px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.summary-value {
  font-size: 3rem;
  font-weight: bold;
  margin: 15px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.05);
  position: relative;

  background: linear-gradient(45deg, currentColor 30%, rgba(0,0,0,0.5) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تم إزالة تأثير hover للقيم لتحسين الأداء */

.summary-unit {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin-top: 5px;
  font-weight: 500;
  position: relative;
}

.summary-unit::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  left: 50%;
  margin-left: -20px;
  width: 40px;
  height: 2px;
  background-color: currentColor;
  opacity: 0.3;
  border-radius: 2px;
}

/* ===== تنسيقات نهائية لضمان اللون الأبيض لبطاقات المساهمات ===== */
/* هذه التنسيقات في نهاية الملف لضمان الأولوية القصوى */

html body.contributions-page div#contribution-reports div.summary-card {
  background: #ffffff !important;
  background-color: #ffffff !important;
  background-image: none !important;
  color: #333333 !important;
  border: 2px solid #e0e0e0 !important;
}

html body.contributions-page div#contribution-reports div.summary-card div.summary-value {
  color: #333333 !important;
  background: none !important;
  background-image: none !important;
  background-color: transparent !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: #333333 !important;
  background-clip: unset !important;
}

html body.contributions-page div#contribution-reports div.summary-card div.summary-title {
  color: #333333 !important;
}

html body.contributions-page div#contribution-reports div.summary-card div.summary-unit {
  color: #666666 !important;
}

html body.contributions-page div#contribution-reports div.summary-card div.card-icon {
  color: #333333 !important;
  background: rgba(240, 240, 240, 0.8) !important;
  border: 1px solid #ddd !important;
}

html body.contributions-page div#contribution-reports div.summary-card::before {
  background: #ffffff !important;
  background-image: none !important;
}

.details-btn {
  background-color: #ffffff;
  color: #333333;
  border: 1px solid #ddd;
  border-radius: 30px;
  padding: 10px 20px;
  cursor: pointer;

  font-weight: bold;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.details-btn::after {
  content: '\f105';
  font-family: 'Font Awesome 5 Free';
  margin-right: 8px;
  font-size: 14px;
}

.details-btn:hover {
  background-color: #2980b9;
}

/* تنسيق فلاتر البحث المحددة */
.search-filters-container,
.filtered-search-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.filter-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  direction: rtl;
  text-align: right;
}

.filter-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.filter-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.search-btn {
  background-color: #007bff;
  color: white;
}

.search-btn:hover {
  background-color: #0056b3;
}

.reset-btn {
  background-color: #6c757d;
  color: white;
}

.reset-btn:hover {
  background-color: #545b62;
}

.export-btn {
  background-color: #28a745;
  color: white;
}

.export-btn:hover {
  background-color: #1e7e34;
}