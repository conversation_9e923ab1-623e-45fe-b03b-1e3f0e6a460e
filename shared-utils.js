// ملف الدوال المساعدة المشتركة
// يحتوي على الدوال المكررة في عدة ملفات لتجنب التكرار

// ===== دوال إدارة المستخدم =====

// تحديث عرض اسم المستخدم في الواجهة
function updateUserDisplay() {
  const userNameElement = document.getElementById('userName');
  if (userNameElement) {
    // البحث عن اسم المستخدم من مصادر متعددة
    const username = localStorage.getItem('username') ||
                    localStorage.getItem('userName') ||
                    'المستخدم الحالي';

    userNameElement.textContent = username;
    console.log('تم تحديث عرض المستخدم:', username);
  }
}

// الحصول على اسم المستخدم الحالي
function getCurrentUsername() {
  return localStorage.getItem('username') ||
         localStorage.getItem('userName') ||
         'مجهول';
}

// ===== دوال البحث والفلترة =====

// تنظيف النص العربي للبحث
function normalizeArabicText(text) {
  if (!text) return '';
  return text
    .replace(/[أإآا]/g, 'ا')
    .replace(/ة/g, 'ه')
    .replace(/ى/g, 'ي')
    .replace(/ؤ/g, 'و')
    .replace(/ئ/g, 'ي')
    .replace(/ء/g, '')
    .replace(/\s+/g, ' ')
    .trim();
}

// البحث المتقدم في الموظفين
function advancedEmployeeSearch(employee, searchText, department, job) {
  const normalizedSearch = normalizeArabicText(searchText.toLowerCase());
  const normalizedCode = normalizeArabicText(employee.code?.toString().toLowerCase() || '');
  const normalizedName = normalizeArabicText(employee.full_name?.toLowerCase() || '');
  const normalizedDepartment = normalizeArabicText(employee.department?.toLowerCase() || '');
  const normalizedJob = normalizeArabicText(employee.job_title?.toLowerCase() || '');

  const matchesSearch = !searchText || 
    normalizedCode.includes(normalizedSearch) ||
    normalizedName.includes(normalizedSearch);

  const matchesDepartment = !department || 
    normalizedDepartment === normalizeArabicText(department.toLowerCase());

  const matchesJob = !job || 
    normalizedJob === normalizeArabicText(job.toLowerCase());

  return matchesSearch && matchesDepartment && matchesJob;
}

// البحث السريع في الجداول
function quickTableSearch(searchInput, tableBodyId) {
  if (!searchInput) return;
  
  searchInput.addEventListener('input', function() {
    const searchTerm = this.value.trim().toLowerCase();
    const tableBody = document.getElementById(tableBodyId);
    if (!tableBody) return;
    
    const rows = tableBody.querySelectorAll('tr');
    
    rows.forEach(row => {
      if (searchTerm === '') {
        row.style.display = '';
      } else {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
      }
    });
  });
}

// ===== إدارة التبويبات =====

// إعداد التبويبات الموحد
function setupUnifiedTabs() {
  const tabBtns = document.querySelectorAll('.tab-btn');
  const tabContents = document.querySelectorAll('.tab-content');
  
  tabBtns.forEach(btn => {
    btn.addEventListener('click', function(e) {
      e.preventDefault();
      
      // التحقق من أن الزر مرئي وغير معطل
      if (this.style.display === 'none' || this.disabled) {
        return;
      }
      
      const targetTab = this.dataset.tab;
      if (!targetTab) return;
      
      // إزالة الفئة النشطة من جميع الأزرار
      tabBtns.forEach(b => b.classList.remove('active'));
      // إضافة الفئة النشطة للزر المحدد
      this.classList.add('active');
      
      // إخفاء جميع محتويات التبويبات
      tabContents.forEach(content => {
        content.style.display = 'none';
      });
      
      // إظهار التبويب المحدد
      const targetContent = document.getElementById(targetTab);
      if (targetContent) {
        targetContent.style.display = 'block';
        
        // إطلاق حدث مخصص عند تغيير التبويب
        const event = new CustomEvent('tabChanged', {
          detail: { tabId: targetTab, tabElement: this }
        });
        document.dispatchEvent(event);
      }
    });
  });
}

// تفعيل تبويب محدد برمجياً
function activateTab(tabId) {
  const tabBtn = document.querySelector(`[data-tab="${tabId}"]`);
  if (tabBtn) {
    tabBtn.click();
  }
}

// ===== إدارة النماذج =====

// ملء حقول الموظف - دالة موحدة ومحسنة
function fillEmployeeFields(employee, fields = null) {
  if (!employee) return;

  // إذا لم يتم تمرير حقول، استخدم الحقول الافتراضية
  if (!fields) {
    fields = {
      code: document.getElementById('employeeCode') || document.getElementById('employee_code'),
      name: document.getElementById('employeeName') || document.getElementById('employee_name'),
      department: document.getElementById('employeeDepartment') || document.getElementById('department'),
      job: document.getElementById('employeeJob') || document.getElementById('job_title'),
      salary: document.getElementById('employeeSalary') || document.getElementById('salary'),
      phone: document.getElementById('employeePhone') || document.getElementById('phone'),
      email: document.getElementById('employeeEmail') || document.getElementById('email')
    };
  }

  if (fields.code) fields.code.value = employee.code || '';
  if (fields.name) fields.name.value = employee.full_name || '';
  if (fields.department) fields.department.value = employee.department || '';
  if (fields.job) fields.job.value = employee.job_title || '';
  if (fields.salary) fields.salary.value = employee.salary || '';
  if (fields.phone) fields.phone.value = employee.phone || '';
  if (fields.email) fields.email.value = employee.email || '';

  // إطلاق أحداث التغيير للحقول
  Object.values(fields).forEach(field => {
    if (field && field.dispatchEvent) {
      field.dispatchEvent(new Event('change', { bubbles: true }));
    }
  });
}

// مسح حقول النموذج
function clearFormFields(fields) {
  if (!fields) return;
  
  Object.values(fields).forEach(field => {
    if (field && field.value !== undefined) {
      field.value = '';
    }
  });
}

// التحقق من صحة النموذج
function validateForm(formId, requiredFields = []) {
  const form = document.getElementById(formId);
  if (!form) return false;
  
  let isValid = true;
  
  // التحقق من الحقول المطلوبة
  requiredFields.forEach(fieldName => {
    const field = form.querySelector(`[name="${fieldName}"]`);
    if (field && !field.value.trim()) {
      field.style.borderColor = '#e74c3c';
      isValid = false;
    } else if (field) {
      field.style.borderColor = '';
    }
  });
  
  return isValid;
}

// ===== إدارة البيانات =====

// تحديث قوائم الفلترة
function updateFilterOptions(employees, departmentSelectId, jobSelectId) {
  const departmentSelect = document.getElementById(departmentSelectId);
  const jobSelect = document.getElementById(jobSelectId);
  
  if (departmentSelect) {
    departmentSelect.innerHTML = '<option value="">كل الإدارات</option>';
    const departments = [...new Set(employees.map(emp => emp.department).filter(Boolean))].sort();
    departments.forEach(dept => {
      const option = document.createElement('option');
      option.value = dept;
      option.textContent = dept;
      departmentSelect.appendChild(option);
    });
  }
  
  if (jobSelect) {
    jobSelect.innerHTML = '<option value="">كل الوظائف</option>';
    const jobs = [...new Set(employees.map(emp => emp.job_title).filter(Boolean))].sort();
    jobs.forEach(job => {
      const option = document.createElement('option');
      option.value = job;
      option.textContent = job;
      jobSelect.appendChild(option);
    });
  }
}

// ===== دوال مساعدة عامة =====

// تنسيق التاريخ - دالة محسنة لمعالجة مشاكل التوقيت
function formatDate(dateString) {
  if (typeof DateUtils !== 'undefined') {
    return DateUtils.formatDateFromDatabase(dateString);
  }

  // إذا لم يكن DateUtils متاح، استخدم تنسيق بسيط
  if (!dateString) return '-';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';

    // عرض التاريخ بالتنسيق DD/MM/YYYY
    return date.toLocaleDateString('en-GB');
  } catch (error) {
    return '-';
  }
}

// تنسيق التاريخ بالعربية (للعرض فقط)
function formatDateArabic(dateString) {
  if (!dateString) return 'غير محدد';

  try {
    // إذا كان التاريخ بصيغة YYYY-MM-DD، استخدمه مباشرة
    if (typeof dateString === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      const [year, month, day] = dateString.split('-');
      return `${day}/${month}/${year}`;
    }

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'تاريخ غير صحيح';

    // استخدام التوقيت المحلي بدلاً من UTC لتجنب مشكلة نقص اليوم
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error('خطأ في تنسيق التاريخ:', error);
    return 'تاريخ غير صحيح';
  }
}

// تنسيق الأرقام
function formatNumber(number) {
  if (!number) return '0';
  return new Intl.NumberFormat('ar-EG').format(number);
}

// عرض القيم مع معالجة null
function displayValue(value) {
  return value === null || value === undefined ? '-' : value;
}

// تأكيد الحذف
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
  return confirm(message);
}

// إظهار رسالة محسنة
function showMessage(message, type = 'info') {
  // إنشاء عنصر الإشعار
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.innerHTML = `
    <div class="notification-content">
      <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
      <span>${message}</span>
    </div>
  `;

  // تطبيق الأنماط
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 10000;
    max-width: 400px;
    word-wrap: break-word;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    background: ${type === 'error' ? '#f44336' : type === 'success' ? '#4CAF50' : type === 'warning' ? '#ff9800' : '#2196F3'};
  `;

  // إضافة الإشعار إلى الصفحة
  document.body.appendChild(notification);

  // إظهار الإشعار
  setTimeout(() => {
    notification.style.transform = 'translateX(0)';
  }, 100);

  // إخفاء الإشعار بعد 4 ثوان
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 4000);
}

// دالة showNotification موحدة (alias لـ showMessage)
function showNotification(message, type = 'info') {
  return showMessage(message, type);
}

// ===== دوال البحث عن الموظفين الموحدة =====

// دالة البحث عن الموظف الموحدة
function handleEmployeeSearch(searchTerm, datalistId, employees, clearFieldsCallback) {
  const datalist = document.getElementById(datalistId);
  if (!datalist) {
    return;
  }

  datalist.innerHTML = '';

  if (!searchTerm || searchTerm.trim() === '') {
    // مسح الحقول عند مسح البحث
    if (clearFieldsCallback) {
      clearFieldsCallback();
    }
    return;
  }

  // التحقق من وجود بيانات الموظفين
  if (!employees || employees.length === 0) {
    const option = document.createElement('option');
    option.value = "لا توجد بيانات موظفين";
    datalist.appendChild(option);
    return;
  }

  // البحث في بيانات الموظفين
  const searchLower = normalizeArabicText(searchTerm.toLowerCase());
  const filteredEmployees = employees.filter(emp => {
    const nameMatch = normalizeArabicText(emp.full_name.toLowerCase()).includes(searchLower);
    const codeMatch = emp.code.toString().includes(searchTerm);
    const deptMatch = emp.department && normalizeArabicText(emp.department.toLowerCase()).includes(searchLower);
    return nameMatch || codeMatch || deptMatch;
  });

  if (filteredEmployees.length > 0) {
    // عرض أول 15 نتيجة فقط لتحسين الأداء
    filteredEmployees.slice(0, 15).forEach(emp => {
      const option = document.createElement('option');
      option.value = `${emp.code} - ${emp.full_name}`;
      datalist.appendChild(option);
    });
  } else {
    // إضافة رسالة إذا لم يتم العثور على نتائج
    const option = document.createElement('option');
    option.value = "لا توجد نتائج مطابقة";
    datalist.appendChild(option);
  }
}

// دالة اختيار الموظف الموحدة
function handleEmployeeSelection(value, employees, fillFieldsCallback) {
  if (!value || value === "لا توجد نتائج مطابقة") return;

  // استخراج الكود من القيمة المحددة
  const codePart = value.split(' - ')[0];
  const employee = employees.find(emp => emp.code == codePart);

  if (employee && fillFieldsCallback) {
    fillFieldsCallback(employee);
  }
}

// دالة ملء فلتر الإدارات الموحدة
function populateDepartmentFilter(employees, filterId = 'departmentFilter', defaultText = 'جميع الإدارات') {
  const filterElement = document.getElementById(filterId);
  if (!filterElement) return;

  const departments = [...new Set(employees.map(emp => emp.department).filter(Boolean))].sort();

  filterElement.innerHTML = `<option value="">${defaultText}</option>`;
  departments.forEach(dept => {
    const option = document.createElement('option');
    option.value = dept;
    option.textContent = dept;
    filterElement.appendChild(option);
  });
}

// معالجة أخطاء API
function handleApiError(error, message) {
  console.error('API Error:', error);
  console.error('Error details:', {
    message: error.message,
    status: error.status,
    statusText: error.statusText
  });
  showMessage(message, 'error');
}

// ===== دوال مشتركة جديدة =====

// نظام التخزين المؤقت للإدارات
let departmentsCache = {
  data: null,
  timestamp: null,
  expiry: 5 * 60 * 1000 // 5 دقائق
};

// تحميل الإدارات مع التخزين المؤقت
async function loadDepartments(forceRefresh = false) {
  try {
    const API_URL = window.CONFIG ? window.CONFIG.API_URL : (localStorage.getItem('serverUrl') || 'http://localhost:5500/api');
    const requestUrl = `${API_URL}/departments`;

    // تتبع الطلب
    const requestCount = trackAPIRequest(requestUrl, 'loadDepartments');

    // التحقق من صحة الكاش
    const now = Date.now();
    if (!forceRefresh && departmentsCache.data && departmentsCache.timestamp &&
        (now - departmentsCache.timestamp) < departmentsCache.expiry) {
      console.log('استخدام الإدارات من الكاش');
      return departmentsCache.data;
    }

    console.log(`جاري تحميل الإدارات من: ${requestUrl} (طلب رقم ${requestCount})`);

    const response = await fetch(requestUrl);
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

    const departments = await response.json();

    // تحديث الكاش
    departmentsCache.data = departments;
    departmentsCache.timestamp = now;

    console.log('تم تحميل الإدارات وحفظها في الكاش:', departments);
    return departments;
  } catch (error) {
    console.error('خطأ في تحميل الإدارات:', error);
    // إرجاع البيانات من الكاش إذا كانت متاحة حتى لو انتهت صلاحيتها
    if (departmentsCache.data) {
      console.log('استخدام الإدارات من الكاش المنتهي الصلاحية');
      return departmentsCache.data;
    }
    throw error;
  }
}

// مسح كاش الإدارات
function clearDepartmentsCache() {
  departmentsCache.data = null;
  departmentsCache.timestamp = null;
  console.log('تم مسح كاش الإدارات');
}

// ===== نظام مراقبة الطلبات =====

// نظام تتبع الطلبات
let requestTracker = {
  requests: {},
  duplicateThreshold: 2, // عدد الطلبات المتكررة المسموح
  timeWindow: 5000 // نافذة زمنية 5 ثوان
};

// تتبع طلب API
function trackAPIRequest(url, source = 'unknown') {
  const now = Date.now();
  const key = url;

  if (!requestTracker.requests[key]) {
    requestTracker.requests[key] = [];
  }

  // إضافة الطلب الجديد
  requestTracker.requests[key].push({
    timestamp: now,
    source: source
  });

  // تنظيف الطلبات القديمة
  requestTracker.requests[key] = requestTracker.requests[key].filter(
    req => (now - req.timestamp) <= requestTracker.timeWindow
  );

  // فحص الطلبات المتكررة
  const recentRequests = requestTracker.requests[key];
  if (recentRequests.length > requestTracker.duplicateThreshold) {
    console.warn(`⚠️ طلبات متكررة مكتشفة: ${url}`);
    console.warn(`عدد الطلبات: ${recentRequests.length} في آخر ${requestTracker.timeWindow/1000} ثانية`);
    console.warn('مصادر الطلبات:', recentRequests.map(r => r.source));

    // إرسال تحذير للمطور
    if (window.console && window.console.trace) {
      console.trace('تتبع مصدر الطلبات المتكررة');
    }
  }

  return recentRequests.length;
}

// الحصول على إحصائيات الطلبات
function getRequestStats() {
  const stats = {};
  const now = Date.now();

  Object.keys(requestTracker.requests).forEach(url => {
    const recentRequests = requestTracker.requests[url].filter(
      req => (now - req.timestamp) <= requestTracker.timeWindow
    );

    if (recentRequests.length > 0) {
      stats[url] = {
        count: recentRequests.length,
        sources: recentRequests.map(r => r.source),
        isDuplicate: recentRequests.length > requestTracker.duplicateThreshold
      };
    }
  });

  return stats;
}

// مسح إحصائيات الطلبات
function clearRequestStats() {
  requestTracker.requests = {};
  console.log('تم مسح إحصائيات الطلبات');
}

// عرض تقرير مفصل عن الطلبات
function showRequestReport() {
  const stats = getRequestStats();
  const hasData = Object.keys(stats).length > 0;

  console.group('📊 تقرير مراقبة الطلبات');

  if (!hasData) {
    console.log('✅ لا توجد طلبات متكررة في الوقت الحالي');
  } else {
    console.log(`📈 إجمالي الطلبات المراقبة: ${Object.keys(stats).length}`);

    Object.entries(stats).forEach(([url, data]) => {
      const status = data.isDuplicate ? '⚠️ مكرر' : '✅ طبيعي';
      console.log(`${status} ${url}: ${data.count} طلبات`);
      if (data.isDuplicate) {
        console.log(`   المصادر: ${data.sources.join(', ')}`);
      }
    });
  }

  console.groupEnd();
  return stats;
}

// إضافة أوامر مطور مفيدة
function addDeveloperCommands() {
  // إضافة أوامر للوحة التحكم
  window.devTools = {
    // مراقبة الطلبات
    showRequestReport,
    getRequestStats,
    clearRequestStats,

    // إدارة الكاش
    clearDepartmentsCache,
    getDepartmentsCache: () => departmentsCache,

    // معلومات النظام
    getSystemInfo: () => ({
      apiUrl: window.CONFIG ? window.CONFIG.API_URL : localStorage.getItem('serverUrl'),
      cacheExpiry: departmentsCache.expiry,
      requestThreshold: requestTracker.duplicateThreshold,
      timeWindow: requestTracker.timeWindow
    })
  };

  console.log('🛠️ أدوات المطور متاحة في window.devTools');
  console.log('استخدم devTools.showRequestReport() لعرض تقرير الطلبات');
}

// تحميل الموظفين (دالة موحدة)
async function loadEmployees(includeResigned = false) {
  try {
    const API_URL = window.CONFIG ? window.CONFIG.API_URL : (localStorage.getItem('serverUrl') || 'http://localhost:5500/api');
    const token = localStorage.getItem('token');

    console.log(`جاري تحميل الموظفين من: ${API_URL}/employees`);

    const url = includeResigned ?
      `${API_URL}/employees` :
      `${API_URL}/employees?include_resigned=false`;

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('استجابة تحميل الموظفين:', response.status);

    if (response.ok) {
      const employees = await response.json();
      console.log(`تم تحميل ${includeResigned ? 'جميع' : 'النشطين من'} الموظفين بنجاح، العدد:`, employees.length);

      // تحديث الحالة العامة إذا كانت متاحة
      if (window.StateManager) {
        window.StateManager.setEmployees(employees);
      }

      return employees;
    } else {
      console.error(`فشل في تحميل الموظفين: ${response.status} ${response.statusText}`);
      throw new Error(`فشل في تحميل الموظفين: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('خطأ في تحميل بيانات الموظفين:', error);
    throw error;
  }
}

// تحديث حالة الخادم (دالة موحدة)
function updateServerStatus(connected) {
  const statusBtn = document.getElementById('serverStatus');
  const toggleBtn = document.getElementById('toggleServer');

  if (!statusBtn || !toggleBtn) return;

  if (connected) {
    statusBtn.textContent = 'حالة السيرفر: متصل';
    statusBtn.className = 'status-btn connected';
    toggleBtn.textContent = 'إيقاف السيرفر';
    toggleBtn.disabled = false;
  } else {
    statusBtn.textContent = 'حالة السيرفر: غير متصل';
    statusBtn.className = 'status-btn disconnected';
    toggleBtn.textContent = 'تشغيل السيرفر';
    toggleBtn.disabled = false;
  }

  // تحديث الحالة العامة
  if (window.StateManager) {
    window.StateManager.setServerStatus(connected);
  }
}

// فحص حالة الخادم
async function checkServerStatus() {
  try {
    const API_URL = window.CONFIG ? window.CONFIG.API_URL : (localStorage.getItem('serverUrl') || 'http://localhost:5500/api');
    const response = await fetch(`${API_URL}/status`, {
      method: 'GET',
      timeout: 5000
    });
    return response.ok;
  } catch (error) {
    console.log('Server is not running:', error.message);
    return false;
  }
}

// تبديل حالة الخادم
async function toggleServer() {
  const toggleBtn = document.getElementById('toggleServer');
  if (toggleBtn) {
    toggleBtn.disabled = true;
  }

  try {
    const currentStatus = window.GlobalState ? window.GlobalState.serverRunning : false;

    if (!currentStatus) {
      // محاولة الاتصال بالخادم
      const isRunning = await checkServerStatus();
      if (isRunning) {
        updateServerStatus(true);
        // تحميل البيانات إذا كانت الدالة متاحة
        if (window.loadEmployees) {
          await window.loadEmployees();
        }
      } else {
        throw new Error('فشل الاتصال بالسيرفر');
      }
    } else {
      // إيقاف الاتصال بالخادم
      updateServerStatus(false);
      const tableBody = document.getElementById('employeeTableBody');
      if (tableBody) {
        tableBody.innerHTML = '';
      }
      // إعادة تعيين البيانات
      if (window.StateManager) {
        window.StateManager.resetState();
      }
    }
  } catch (error) {
    console.error('Server operation failed:', error);
    const currentStatus = window.GlobalState ? window.GlobalState.serverRunning : false;
    alert(currentStatus ? 'فشل في إيقاف السيرفر' : 'فشل في تشغيل السيرفر');
    updateServerStatus(currentStatus);
  } finally {
    if (toggleBtn) {
      toggleBtn.disabled = false;
    }
  }
}

// تحديث قوائم الفلترة (دالة محسنة)
function updateFilterOptions(employees, departmentFilterId = 'departmentFilter', jobFilterId = 'jobFilter') {
  const departmentFilter = document.getElementById(departmentFilterId);
  const jobFilter = document.getElementById(jobFilterId);

  if (!departmentFilter || !jobFilter) return;

  // مسح القوائم الحالية
  departmentFilter.innerHTML = '<option value="">كل الإدارات</option>';
  jobFilter.innerHTML = '<option value="">كل الوظائف</option>';

  // استخراج القيم الفريدة
  const departments = [...new Set(employees.map(emp => emp.department).filter(Boolean))].sort();
  const jobs = [...new Set(employees.map(emp => emp.job_title).filter(Boolean))].sort();

  // إضافة الإدارات
  departments.forEach(dept => {
    const option = document.createElement('option');
    option.value = dept;
    option.textContent = dept;
    departmentFilter.appendChild(option);
  });

  // إضافة الوظائف
  jobs.forEach(job => {
    const option = document.createElement('option');
    option.value = job;
    option.textContent = job;
    jobFilter.appendChild(option);
  });
}

// ===== تصدير الدوال =====

// تصدير جميع الدوال للاستخدام العام
window.SharedUtils = {
  // دوال إدارة المستخدم
  updateUserDisplay,
  getCurrentUsername,

  // دوال البحث
  normalizeArabicText,
  advancedEmployeeSearch,
  quickTableSearch,

  // دوال التبويبات
  setupUnifiedTabs,
  activateTab,

  // دوال النماذج
  fillEmployeeFields,
  clearFormFields,
  validateForm,

  // دوال البيانات
  updateFilterOptions,
  handleEmployeeSearch,
  handleEmployeeSelection,
  populateDepartmentFilter,

  // دوال مساعدة
  formatDate,
  formatDateArabic,
  formatNumber,
  displayValue,
  confirmDelete,
  showMessage,
  showNotification,
  handleApiError,

  // الدوال المشتركة الجديدة
  loadEmployees,
  loadDepartments,
  clearDepartmentsCache,
  trackAPIRequest,
  getRequestStats,
  clearRequestStats,
  updateServerStatus,
  checkServerStatus,
  toggleServer
};

// تصدير الدوال الفردية أيضاً للتوافق مع الكود الموجود
window.updateUserDisplay = updateUserDisplay;
window.getCurrentUsername = getCurrentUsername;
window.normalizeArabicText = normalizeArabicText;
window.advancedEmployeeSearch = advancedEmployeeSearch;
window.fillEmployeeFields = fillEmployeeFields;
window.formatDate = formatDate;
window.formatDateArabic = formatDateArabic;
window.formatNumber = formatNumber;
window.displayValue = displayValue;
window.confirmDelete = confirmDelete;
window.showMessage = showMessage;
window.showNotification = showNotification;
window.handleApiError = handleApiError;

// تصدير الدوال المشتركة الجديدة
window.loadEmployees = loadEmployees;
window.loadDepartments = loadDepartments;
window.clearDepartmentsCache = clearDepartmentsCache;
window.trackAPIRequest = trackAPIRequest;
window.getRequestStats = getRequestStats;
window.clearRequestStats = clearRequestStats;
window.updateServerStatus = updateServerStatus;
window.checkServerStatus = checkServerStatus;
window.toggleServer = toggleServer;
window.handleEmployeeSearch = handleEmployeeSearch;
window.handleEmployeeSelection = handleEmployeeSelection;

// تهيئة أدوات المطور
addDeveloperCommands();
window.populateDepartmentFilter = populateDepartmentFilter;

// تحديث عرض المستخدم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
  // تأخير قصير للتأكد من تحميل العناصر
  setTimeout(updateUserDisplay, 100);
});
