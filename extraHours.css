/* تنسيقات خاصة بقسم الساعات الإضافية فقط - لا تتعارض مع التنسيق العام */

/* تنسيقات النموذج - متوافقة مع التنسيق العام */
.extra-hour-form .form-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-lg, 1.5rem);
  margin-bottom: var(--spacing-xl, 2rem);
  padding: var(--spacing-xl, 2rem);
  background: var(--bg-light, #f8f9fa);
  border-radius: var(--border-radius-lg, 12px);
  border: 1px solid var(--border-light, #e9ecef);
}

.extra-hour-form .form-group.full-width {
  grid-column: 1 / -1;
}

.extra-hour-form .form-actions.full-width {
  grid-column: 1 / -1;
}

/* تنسيقات متجاوبة للنموذج */
@media (max-width: 1200px) {
  .extra-hour-form .form-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 900px) {
  .extra-hour-form .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 600px) {
  .extra-hour-form .form-grid {
    grid-template-columns: 1fr;
  }
}

/* استخدام تنسيقات التبويبات من التنسيق العام - لا حاجة لتنسيقات إضافية */

/* تنسيقات خاصة بجداول الساعات الإضافية */
.added-extra-hours-container h3::before {
  content: '\f017';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  margin-left: 10px;
}

.extra-hours-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* تنسيق الصف المميز (آخر إضافة) */
.extra-hours-table tr:first-child {
  background-color: #e8f5e8 !important;
  border: 2px solid #4CAF50;
}

.extra-hours-table tr:first-child td {
  font-weight: bold;
}

.extra-hours-table th,
.extra-hours-table td {
  padding: 12px;
  text-align: center;
  border-bottom: 1px solid #ddd;
}

.extra-hours-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.extra-hours-table tbody tr:hover {
  background: #f8f9fa;
}

/* تنسيقات نافذة التعديل */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  padding: 20px;
  border-radius: 8px;
  width: 80%;
  max-width: 600px;
  position: relative;
}

.close-btn {
  position: absolute;
  top: 15px;
  left: 20px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.close-btn:hover {
  color: #333;
}

/* تنسيق فلاتر البحث المحددة للساعات الإضافية */
.search-filters-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.filter-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  direction: rtl;
  text-align: right;
}

.filter-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.filter-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.search-btn {
  background-color: #007bff;
  color: white;
}

.search-btn:hover {
  background-color: #0056b3;
}

.reset-btn {
  background-color: #6c757d;
  color: white;
}

.reset-btn:hover {
  background-color: #545b62;
}
