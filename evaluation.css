/* تنسيقات خاصة بقسم التقييم */
.evaluation-page {
  background: var(--bg-light);
  direction: rtl;
  text-align: right;
}

/* تحسين مظهر عمود الإدارة وعمود الملاحظات في الجداول */
.department-column {
  background: var(--primary-light);
  font-weight: bold;
  color: var(--primary-dark);
  white-space: nowrap;
}

.notes-column {
  background: var(--bg-light);
  color: var(--text-secondary);
  font-style: italic;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: pre-line;
}

/* تنسيق فلاتر البحث المحددة للتقييمات */
.search-filters-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.filter-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  direction: rtl;
  text-align: right;
}

.filter-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.filter-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.search-btn {
  background-color: #007bff;
  color: white;
}

.search-btn:hover {
  background-color: #0056b3;
}

.reset-btn {
  background-color: #6c757d;
  color: white;
}

.reset-btn:hover {
  background-color: #545b62;
}

.export-btn {
  background-color: #28a745;
  color: white;
}

.export-btn:hover {
  background-color: #1e7e34;
}

/* تنسيق الصف المميز (آخر إضافة) لجدول التقييمات */
#evaluation-table tr:first-child {
  background-color: #e8f5e8 !important;
  border: 2px solid #4CAF50;
}

#evaluation-table tr:first-child td {
  font-weight: bold;
}